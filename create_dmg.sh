#!/bin/bash

# 設置顏色輸出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 設置應用程序名稱
APP_NAME="Astreal"
VERSION="1.0.0"

echo -e "${YELLOW}開始構建 $APP_NAME macOS 應用程序...${NC}"

# 清理之前的構建
echo -e "${YELLOW}清理之前的構建...${NC}"
flutter clean

# 獲取依賴
echo -e "${YELLOW}獲取依賴...${NC}"
flutter pub get

# 構建 macOS 應用程序
echo -e "${YELLOW}構建 macOS 應用程序...${NC}"
flutter build macos --release

# 檢查構建是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}構建失敗！${NC}"
    exit 1
fi

# 設置路徑
BUILD_DIR="build/macos/Build/Products/Release"
APP_PATH="$BUILD_DIR/$APP_NAME.app"
DMG_PATH="$APP_NAME-$VERSION.dmg"

echo -e "${GREEN}應用程序構建成功！${NC}"
echo -e "${YELLOW}正在創建 DMG 安裝檔...${NC}"

# 創建臨時目錄
TEMP_DIR="build/dmg_temp"
mkdir -p "$TEMP_DIR"

# 複製應用程序到臨時目錄
cp -R "$APP_PATH" "$TEMP_DIR/"

# 創建 Applications 符號連結
ln -s /Applications "$TEMP_DIR/Applications"

# 創建 DMG 文件
hdiutil create -volname "$APP_NAME" -srcfolder "$TEMP_DIR" -ov -format UDZO "$DMG_PATH"

# 檢查 DMG 創建是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}DMG 創建失敗！${NC}"
    exit 1
fi

# 清理臨時目錄
rm -rf "$TEMP_DIR"

echo -e "${GREEN}DMG 安裝檔創建成功！${NC}"
echo -e "${GREEN}DMG 文件位置: $(pwd)/$DMG_PATH${NC}"

# 打開包含 DMG 文件的目錄
open .

exit 0
