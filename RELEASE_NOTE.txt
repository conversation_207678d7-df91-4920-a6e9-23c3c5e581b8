Release Note:
feat: ?�主?��?置�??��??��?點查詢�?UI?��?

此�?交為?�主?��?置�??��?了以下�??��??�進�?

*   **?��??��?點查詢�?** 使用?�現?�可以查詢特定�??��?（出?��??�、當?��??��??��??��?）�??�主?��?訊�??�含?�?��?座、�?主�??�度?�、�??�進度以�??��??��?資�???*   **UI/UX ?��?�?*
    *   ?��?主�??�置法�?話�?中新增「查詢�??��??��??��?    *   ?�詢結�??�以?��??��??��?展示，�??�查詢�??��??��?主�?詳細資�???    *   ?��?表�??�新增�?座�??��?視覺?�示（�??��??��?框�?標籤）�?    *   ?��?表�??�顯示�?座�??��?，�?額�?顯示該�?座�?總�??��?一度�??�?��???    *   ?��?顯示?��??�更?�詳細�??�含天、�??�、�??��?    *   ?��?表�??��??��??��??��??��?顯示對�??��?座�?度數??    *   使用?�座顏色來�??��??��??�座?��???*   **計�??�輯調整�?*
    *   `_calculateFullTermRulerTimeline` ?��??�更?�為 `calculateFullTermRulerTimeline` 並公?��?    *   ?��?計�??�用?�精確�??��?來�??��?    *   ?��? `_calculateCurrentTermAtTime` ?��?來�?算�?定�??��??��?主�?資�???
?��?變更?�在?��??��?活�??�主?��??��??��?並�??�使?�者�?驗�?
