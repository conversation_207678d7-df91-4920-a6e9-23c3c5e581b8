import 'package:flutter/material.dart';
import '../viewmodels/chart_viewmodel.dart';

/// 界主星配置法卡片組件
class TermRulerProgressionCard extends StatelessWidget {
  final Map<String, dynamic> result;
  final ChartViewModel viewModel;
  final VoidCallback onClose;

  const TermRulerProgressionCard({
    Key? key,
    required this.result,
    required this.viewModel,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentInfo = result['currentInfo'] as Map<String, dynamic>? ?? {};
    final nextTermInfo = result['nextTermInfo'] as Map<String, dynamic>? ?? {};
    final timeline = result['timeline'] as List<Map<String, dynamic>>? ?? [];
    final latitude = result['latitude'] as double? ?? 0.0;
    final longitude = result['longitude'] as double? ?? 0.0;
    final ascendantLongitude = result['ascendantLongitude'] as double? ?? 0.0;

    return Container(
      width: 320,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 標題欄
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.deepPurple.shade400, Colors.deepPurple.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '界主星配置法',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: onClose,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                  tooltip: '關閉',
                ),
              ],
            ),
          ),

          // 可滾動的內容部分
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              clipBehavior: Clip.antiAlias,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 地理資訊卡片
                    _buildInfoCard(
                      title: '地理資訊',
                      icon: Icons.location_on,
                      color: Colors.teal,
                      children: [
                        _buildInfoRow(
                          icon: Icons.explore,
                          label: '緯度',
                          value: '${latitude.toStringAsFixed(4)}° ${latitude >= 0 ? "北" : "南"}',
                        ),
                        _buildInfoRow(
                          icon: Icons.explore,
                          label: '經度',
                          value: '${longitude.toStringAsFixed(4)}° ${longitude >= 0 ? "東" : "西"}',
                        ),
                        _buildInfoRow(
                          icon: Icons.trending_up,
                          label: '上升點經度',
                          value: '${ascendantLongitude.toStringAsFixed(4)}°',
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 出生時間資訊卡片
                    _buildInfoCard(
                      title: '出生時間資訊',
                      icon: Icons.access_time,
                      color: Colors.indigo,
                      children: [
                        _buildInfoRow(
                          icon: Icons.star,
                          label: '上升星座',
                          value: '${currentInfo['ascendantSign'] ?? '未知'}',
                        ),
                        _buildInfoRow(
                          icon: Icons.auto_awesome,
                          label: '上升界主星',
                          value: '${currentInfo['ascendantTermRulerName'] ?? '未知'}',
                        ),
                        _buildInfoRow(
                          icon: Icons.straighten,
                          label: '當前度數',
                          value: '${(currentInfo['currentDegreeInSign'] as double? ?? 0.0).toStringAsFixed(2)}°',
                        ),
                        _buildInfoRow(
                          icon: Icons.schedule,
                          label: '每度時間',
                          value: '${(currentInfo['timePerDegree'] as double? ?? 0.0).toStringAsFixed(4)} 天',
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 下一個界主星卡片
                    _buildInfoCard(
                      title: '下一個界主星',
                      icon: Icons.arrow_forward,
                      color: Colors.orange,
                      children: [
                        _buildInfoRow(
                          icon: Icons.auto_awesome,
                          label: '界主星',
                          value: '${nextTermInfo['nextTermRulerName'] ?? '未知'}',
                        ),
                        _buildInfoRow(
                          icon: Icons.straighten,
                          label: '需要度數',
                          value: '${(nextTermInfo['degreesToNextTerm'] as double? ?? 0.0).toStringAsFixed(2)}°',
                        ),
                        _buildInfoRow(
                          icon: Icons.schedule,
                          label: '需要時間',
                          value: '${(nextTermInfo['timeToNextTermDays'] as double? ?? 0.0).toStringAsFixed(2)} 天',
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 時間表標題
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.purple.shade50, Colors.purple.shade100],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.purple.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.timeline,
                            color: Colors.purple.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '界主星時間表',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.purple.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // 時間表項目
                    ...timeline.map((term) => _buildTermItem(context, term)).toList(),
                  ],
                ),
              ),
            ),
          ),

          // 底部按鈕區域
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _showStartTimeSettings(context, result),
                  icon: const Icon(Icons.search, size: 16),
                  label: const Text('查詢時間點'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple.shade100,
                    foregroundColor: Colors.deepPurple.shade700,
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建信息卡片
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片標題
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          // 卡片內容
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: valueColor ?? Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建時間表項目
  Widget _buildTermItem(BuildContext context, Map<String, dynamic> term) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                size: 16,
                color: Colors.deepPurple.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                '${term['termRulerName'] ?? '未知'} (${term['sign'] ?? '未知'})',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${term['startDateTime'] != null ? viewModel.formatDateTime(term['startDateTime']) : '未知'} - ${term['endDateTime'] != null ? viewModel.formatDateTime(term['endDateTime']) : '未知'}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            '持續: ${(term['durationDays'] as double? ?? 0.0).toStringAsFixed(2)} 天',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示開始時間設定對話框（簡化版）
  void _showStartTimeSettings(BuildContext context, Map<String, dynamic> result) {
    // 這裡可以實現一個簡化的時間設定對話框
    // 或者調用原來的方法
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('時間設定功能開發中...')),
    );
  }
}
