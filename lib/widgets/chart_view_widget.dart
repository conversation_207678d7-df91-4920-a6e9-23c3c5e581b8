import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/viewmodels/chart_viewmodel.dart';
import 'package:astreal/widgets/chart_painter.dart';
import 'package:astreal/widgets/draggable_planet_detail_card.dart';
import 'package:astreal/widgets/dual_chart_painter.dart';
import 'package:astreal/widgets/firdaria_chart_painter.dart';
import 'package:astreal/widgets/person_info_widget.dart';
import 'package:astreal/widgets/time_adjustment_widget.dart';
import 'package:flutter/material.dart';

import '../models/planet_position.dart';

class ChartViewWidget extends StatefulWidget {
  final ChartViewModel viewModel;

  const ChartViewWidget({
    Key? key,
    required this.viewModel,
  }) : super(key: key);

  @override
  State<ChartViewWidget> createState() => _ChartViewWidgetState();
}

class _ChartViewWidgetState extends State<ChartViewWidget> {
  // 存儲最後點擊的行星
  PlanetPosition? _lastHitPlanet;

  // 用於顯示行星詳情卡片的 OverlayEntry
  OverlayEntry? _overlayEntry;

  // 緩存 CustomPainter 實例
  CustomPainter? _cachedPainter;

  /// 加載法達盤數據
  Future<void> _loadFirdariaData() async {
    try {
      // 計算法達盤數據
      await widget.viewModel.calculateFirdaria();

      // 數據加載完成後滾動到當前週期
      if (mounted) {
        _getChartPainter();
      }
    } catch (e) {
      print('加載法達盤數據時出錯: $e');
    }
  }

  @override
  void didUpdateWidget(ChartViewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果圖表類型發生變化，清除緩存的 CustomPainter 實例
    if (oldWidget.viewModel.chartType != widget.viewModel.chartType ||
        oldWidget.viewModel.chartData != widget.viewModel.chartData) {
      print('didUpdateWidget: 清除緩存的 CustomPainter 實例');
      _cachedPainter = null;
    }

    if (widget.viewModel.chartType == ChartType.firdaria) {
      // 檢查是否已經有法達盤數據，如果沒有則計算
      if (widget.viewModel.firdariaData == null ||
          widget.viewModel.firdariaData!.isEmpty) {
        _loadFirdariaData();
      }
    }
  }

  @override
  void dispose() {
    // 確保在 widget 銷毀時移除 overlay
    _removeOverlay();
    super.dispose();
  }

  // 移除 overlay
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  /// 顯示界主星配置法
  void _showTermRulerProgression(BuildContext context) async {
    try {
      // 顯示載入對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 計算界主星配置法時間表
      final Map<String, dynamic> result =
          await widget.viewModel.calculateTermRulerProgressionTimeline();

      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示結果對話框
      if (mounted) {
        showDialog(
          context: context,
          barrierColor: Colors.transparent, // 移除背景遮罩
          builder: (context) {
            final screenWidth = MediaQuery.of(context).size.width;
            final dialogMaxWidth =
                screenWidth < 640 ? screenWidth * 0.9 : 600.0;

            return _DraggableTransparentDialog(
              maxWidth: dialogMaxWidth * 0.6, // 縮小寬度到 80%
              child: _buildTermRulerProgressionDialog(context, result),
            );
          },
        );
      }
    } catch (e) {
      // 關閉載入對話框
      if (mounted) Navigator.of(context).pop();

      // 顯示錯誤對話框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('錯誤'),
            content: Text('計算界主星配置法失敗：$e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('確定'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// 建立界主星配置法對話框
  Widget _buildTermRulerProgressionDialog(
      BuildContext context, Map<String, dynamic> result) {
    final currentInfo = result['currentInfo'] as Map<String, dynamic>;
    final timeline = result['timeline'] as List<Map<String, dynamic>>;

    final nextTermInfo = currentInfo['nextTermInfo'] as Map<String, dynamic>;

    // 獲取緯度和經度資訊
    final latitude = result['latitude'] as double;
    final longitude = result['longitude'] as double;
    final ascendantLongitude = result['ascendantLongitude'] as double;

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: Colors.white,
      elevation: 8,
      title: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.deepPurple.shade400, Colors.deepPurple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.deepPurple.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                '界主星配置法',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
              ),
            ),
          ],
        ),
      ),
      titlePadding: const EdgeInsets.fromLTRB(20, 20, 20, 8),
      contentPadding: const EdgeInsets.fromLTRB(20, 8, 20, 8),
      actionsPadding: const EdgeInsets.fromLTRB(20, 8, 20, 16),
      content: Container(
        width: double.infinity,
        height: 450,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 地理資訊卡片
              _buildInfoCard(
                title: '地理資訊',
                icon: Icons.location_on,
                color: Colors.teal,
                children: [
                  _buildInfoRow(
                    icon: Icons.explore,
                    label: '緯度',
                    value: '${latitude.toStringAsFixed(4)}° ${latitude >= 0 ? "北" : "南"}',
                  ),
                  _buildInfoRow(
                    icon: Icons.explore,
                    label: '經度',
                    value: '${longitude.toStringAsFixed(4)}° ${longitude >= 0 ? "東" : "西"}',
                  ),
                  _buildInfoRow(
                    icon: Icons.trending_up,
                    label: '上升點經度',
                    value: '${ascendantLongitude.toStringAsFixed(4)}°',
                  ),
                ],
              ),

              // 緯度索引調試資訊
              if (currentInfo['latitudeDebugInfo'] != null) ...[
                const SizedBox(height: 6), // 縮小間距
                Container(
                  width: double.infinity, // 寬度滿版
                  padding: const EdgeInsets.all(6), // 縮小內邊距
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    border: Border.all(color: Colors.grey.withOpacity(0.3)),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '緯度索引調試',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                      const SizedBox(height: 2), // 縮小間距
                      Text(
                        '使用緯度：${(currentInfo['latitudeDebugInfo']['inputLatitude'] as double).toStringAsFixed(4)}°',
                        style: const TextStyle(fontSize: 11),
                      ),
                      Text(
                        '表格索引：${currentInfo['latitudeDebugInfo']['calculatedIndex']}',
                        style: const TextStyle(fontSize: 11),
                      ),
                      Text(
                        '對應：${currentInfo['latitudeDebugInfo']['description']}',
                        style: const TextStyle(fontSize: 11),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 8), // 縮小間距

              // 查詢時間點的界主星資訊（如果有的話）
              if (result['currentTermAtTime'] != null) ...[
                Container(
                  padding: const EdgeInsets.all(8), // 縮小內邊距
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '查詢時間點的界主星',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                        ),
                      ),
                      const SizedBox(height: 4), // 縮小間距
                      _buildCurrentTermAtTimeInfo(
                          result['currentTermAtTime'] as Map<String, dynamic>),
                    ],
                  ),
                ),
                const SizedBox(height: 8), // 縮小間距
              ],

              // 出生時間資訊卡片
              _buildInfoCard(
                title: '出生時間資訊',
                icon: Icons.access_time,
                color: Colors.indigo,
                children: [
                  _buildInfoRow(
                    icon: Icons.star,
                    label: '上升星座',
                    value: '${currentInfo['ascendantSign']}',
                  ),
                  _buildInfoRow(
                    icon: Icons.auto_awesome,
                    label: '上升界主星',
                    value: '${currentInfo['ascendantTermRulerName']}',
                  ),
                  _buildInfoRow(
                    icon: Icons.straighten,
                    label: '當前度數',
                    value: '${(currentInfo['currentDegreeInSign'] as double).toStringAsFixed(2)}°',
                  ),
                  _buildInfoRow(
                    icon: Icons.schedule,
                    label: '每度時間',
                    value: '${(currentInfo['timePerDegree'] as double).toStringAsFixed(4)} 天',
                  ),
                  _buildInfoRow(
                    icon: Icons.timelapse,
                    label: '整個星座時間',
                    value: '${(currentInfo['totalSignTime'] as double).toStringAsFixed(2)} 天',
                  ),
                  _buildInfoRow(
                    icon: Icons.public,
                    label: '半球',
                    value: '${currentInfo['isNorth'] ? "北半球" : "南半球"}',
                  ),
                ],
              ),

              const SizedBox(height: 8), // 縮小間距

              // 下一個界主星卡片
              _buildInfoCard(
                title: '下一個界主星',
                icon: Icons.arrow_forward,
                color: Colors.orange,
                children: [
                  _buildInfoRow(
                    icon: Icons.auto_awesome,
                    label: '界主星',
                    value: '${nextTermInfo['nextTermRulerName']}',
                  ),
                  _buildInfoRow(
                    icon: Icons.straighten,
                    label: '需要度數',
                    value: '${(nextTermInfo['degreesToNextTerm'] as double).toStringAsFixed(2)}°',
                  ),
                  _buildInfoRow(
                    icon: Icons.schedule,
                    label: '需要時間',
                    value: '${(nextTermInfo['timeToNextTermDays'] as double).toStringAsFixed(2)} 天',
                  ),
                  if (nextTermInfo['crossesSign'] == true)
                    _buildInfoRow(
                      icon: Icons.star,
                      label: '目標星座',
                      value: '${nextTermInfo['targetSign']}',
                      valueColor: Colors.orange,
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // 時間表標題
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.purple.shade50, Colors.purple.shade100],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.purple.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.timeline,
                      color: Colors.purple.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '界主星時間表',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),

              ...timeline.map((term) => _buildTermItem(context, term)).toList(),
            ],
          ),
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ElevatedButton.icon(
              onPressed: () => _showStartTimeSettings(context, result),
              icon: const Icon(Icons.search, size: 16),
              label: const Text('查詢時間點'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple.shade100,
                foregroundColor: Colors.deepPurple.shade700,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close, size: 16),
              label: const Text('關閉'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade100,
                foregroundColor: Colors.grey.shade700,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 顯示開始時間設定對話框
  void _showStartTimeSettings(
      BuildContext context, Map<String, dynamic> originalResult) async {
    DateTime selectedStartTime = originalResult['birthDateTime'] as DateTime;

    final result = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('查詢指定時間的界主星'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('選擇要查詢的時間點：'),
                  const Text(
                    '系統會顯示該時間點落在哪個星座哪個界',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const SizedBox(height: 16),

                  // 預設選項
                  ListTile(
                    title: const Text('出生時間'),
                    subtitle: Text(widget.viewModel.formatDateTime(
                        originalResult['birthDateTime'] as DateTime)),
                    leading: Radio<DateTime>(
                      value: originalResult['birthDateTime'] as DateTime,
                      groupValue: selectedStartTime,
                      onChanged: (DateTime? value) {
                        if (value != null) {
                          setState(() {
                            selectedStartTime = value;
                          });
                        }
                      },
                    ),
                  ),

                  ListTile(
                    title: const Text('當前時間'),
                    subtitle:
                        Text(widget.viewModel.formatDateTime(DateTime.now())),
                    leading: Radio<DateTime>(
                      value: DateTime.now(),
                      groupValue: selectedStartTime,
                      onChanged: (DateTime? value) {
                        if (value != null) {
                          setState(() {
                            selectedStartTime = value;
                          });
                        }
                      },
                    ),
                  ),

                  const Divider(),

                  // 自訂時間
                  ListTile(
                    title: const Text('自訂時間'),
                    subtitle: Text(
                        widget.viewModel.formatDateTime(selectedStartTime)),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: selectedStartTime,
                          firstDate: DateTime(1900),
                          lastDate: DateTime(2100),
                        );

                        if (date != null) {
                          final time = await showTimePicker(
                            context: context,
                            initialTime:
                                TimeOfDay.fromDateTime(selectedStartTime),
                          );

                          if (time != null) {
                            setState(() {
                              selectedStartTime = DateTime(
                                date.year,
                                date.month,
                                date.day,
                                time.hour,
                                time.minute,
                              );
                            });
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(selectedStartTime),
                  child: const Text('確定'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result != null) {
      // 重新計算界主星配置法，使用新的開始時間
      await _recalculateWithNewStartTime(context, originalResult, result);
    }
  }

  /// 使用新的開始時間重新計算界主星配置法
  Future<void> _recalculateWithNewStartTime(BuildContext context,
      Map<String, dynamic> originalResult, DateTime newStartTime) async {
    try {
      // 顯示載入狀態
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('重新計算中...'),
            ],
          ),
        ),
      );

      // 重新計算時間表，使用新的開始時間
      final latitude = originalResult['latitude'] as double;
      final ascendantLongitude = originalResult['ascendantLongitude'] as double;
      final isNorth = latitude >= 0;

      // 計算新的時間表
      final newTimeline = widget.viewModel.calculateFullTermRulerTimeline(
        ascendantLongitude,
        latitude,
        isNorth,
        newStartTime,
      );

      // 計算指定時間點的界主星資訊
      final currentTermInfo = _calculateCurrentTermAtTime(
        newStartTime,
        newTimeline,
        ascendantLongitude,
        latitude,
        isNorth,
      );

      // 更新結果
      final newResult = Map<String, dynamic>.from(originalResult);
      newResult['timeline'] = newTimeline;
      newResult['birthDateTime'] = newStartTime;
      newResult['queryDateTime'] = newStartTime; // 添加查詢時間
      newResult['currentTermAtTime'] = currentTermInfo;

      // 關閉載入對話框
      Navigator.of(context).pop();

      // 顯示新的結果
      showDialog(
        context: context,
        builder: (context) =>
            _buildTermRulerProgressionDialog(context, newResult),
      );
    } catch (e) {
      // 關閉載入對話框
      Navigator.of(context).pop();

      // 顯示錯誤
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('重新計算失敗：$e')),
      );
    }
  }

  /// 計算指定時間點的界主星資訊
  Map<String, dynamic> _calculateCurrentTermAtTime(
    DateTime targetTime,
    List<Map<String, dynamic>> timeline,
    double ascendantLongitude,
    double latitude,
    bool isNorth,
  ) {
    // 找到目標時間所在的界
    for (final term in timeline) {
      final startDateTime = term['startDateTime'];
      final endDateTime = term['endDateTime'];

      // 安全的類型檢查和轉換
      if (startDateTime is! DateTime || endDateTime is! DateTime) {
        continue;
      }

      if (targetTime.isAfter(startDateTime) &&
          targetTime.isBefore(endDateTime)) {
        // 計算在這個界中的進度
        final totalDuration = endDateTime.difference(startDateTime);
        final elapsed = targetTime.difference(startDateTime);
        final progress = totalDuration.inMinutes > 0
            ? elapsed.inMinutes / totalDuration.inMinutes
            : 0.0;

        final startDegree = (term['startDegree'] as num?)?.toDouble() ?? 0.0;
        final endDegree = (term['endDegree'] as num?)?.toDouble() ?? 0.0;
        final currentDegree =
            startDegree + (endDegree - startDegree) * progress;
        final degreeDifference = endDegree - startDegree;

        // 計算剩餘時間
        final remaining = endDateTime.difference(targetTime);
        final remainingDays = remaining.inMinutes / (24 * 60);

        // 計算每度所需時間
        final durationDays = (term['durationDays'] as num?)?.toDouble() ?? 0.0;
        final timePerDegree =
            degreeDifference > 0 ? durationDays / degreeDifference : 0.0;

        return {
          'termRuler': term['termRuler'],
          'termRulerName': term['termRulerName'] ?? '未知',
          'sign': term['sign'] ?? '未知',
          'currentDegree': currentDegree,
          'startDegree': startDegree,
          'endDegree': endDegree,
          'degreeDifference': degreeDifference,
          'timePerDegree': timePerDegree,
          'progress': progress,
          'remainingDays': remainingDays,
          'startDateTime': startDateTime,
          'endDateTime': endDateTime,
          'targetTime': targetTime,
          'durationDays': durationDays,
        };
      }
    }

    // 如果沒有找到，返回空資訊
    return {
      'termRuler': null,
      'termRulerName': '未知',
      'sign': '未知',
      'currentDegree': 0.0,
      'startDegree': 0.0,
      'endDegree': 0.0,
      'degreeDifference': 0.0,
      'timePerDegree': 0.0,
      'progress': 0.0,
      'remainingDays': 0.0,
      'startDateTime': null,
      'endDateTime': null,
      'targetTime': targetTime,
      'durationDays': 0.0,
    };
  }

  /// 格式化持續時間為「X天X小時X分」格式
  String _formatDuration(double durationDays) {
    final totalMinutes = (durationDays * 24 * 60).round();
    final days = totalMinutes ~/ (24 * 60);
    final hours = (totalMinutes % (24 * 60)) ~/ 60;
    final minutes = totalMinutes % 60;

    final parts = <String>[];
    if (days > 0) parts.add('${days}天');
    if (hours > 0) parts.add('${hours}小時');
    if (minutes > 0) parts.add('${minutes}分');

    return parts.isEmpty ? '0分' : parts.join('');
  }

  /// 格式化星座一度所需時間
  String _formatSignTimePerDegree(double signTotalDays) {
    final timePerDegree = signTotalDays / 30.0; // 30度一個星座
    final durationText = _formatDuration(timePerDegree);
    return '${timePerDegree.toStringAsFixed(5)}天($durationText)';
  }

  /// 格式化雙格式時間：X.XXXXX天(X天X小時X分)
  String _formatDualTime(double durationDays) {
    final durationText = _formatDuration(durationDays);
    return '${durationDays.toStringAsFixed(5)}天($durationText)';
  }

  /// 格式化日期時間並包含星座度數
  String _formatDateTimeWithSign(
      DateTime dateTime, String sign, double degree) {
    final year = dateTime.year;
    final month = dateTime.month.toString().padLeft(2, '0');
    final day = dateTime.day.toString().padLeft(2, '0');
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');

    return '$year/$month/$day:$hour:$minute $sign${degree.toStringAsFixed(0)}°';
  }

  /// 建立查詢時間點的界主星資訊
  Widget _buildCurrentTermAtTimeInfo(Map<String, dynamic> termInfo) {
    final termRulerName = termInfo['termRulerName'] as String;
    final sign = termInfo['sign'] as String;
    final currentDegree = termInfo['currentDegree'] as double;
    final startDegree = termInfo['startDegree'] as double;
    final endDegree = termInfo['endDegree'] as double;
    final degreeDifference = termInfo['degreeDifference'] as double;
    final timePerDegree = termInfo['timePerDegree'] as double;
    final progress = termInfo['progress'] as double;
    final remainingDays = termInfo['remainingDays'] as double;
    final durationDays = termInfo['durationDays'] as double;
    final targetTime = termInfo['targetTime'] as DateTime;
    final startDateTime = termInfo['startDateTime'];
    final endDateTime = termInfo['endDateTime'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('查詢時間：${widget.viewModel.formatDateTime(targetTime)}'),
        const SizedBox(height: 8),

        // 界主星資訊
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('所在星座：$sign'),
              Text('當前界主星：$termRulerName'),
              Text('當前度數：${currentDegree.toStringAsFixed(2)}°'),
              Text('界內進度：${(progress * 100).toStringAsFixed(1)}%'),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // 時間資訊
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (startDateTime != null && endDateTime != null) ...[
                Text(
                    '開始時間：${widget.viewModel.formatDateTime(startDateTime as DateTime)}'),
                Text(
                    '結束時間：${widget.viewModel.formatDateTime(endDateTime as DateTime)}'),
              ],
              Text('界總持續：${_formatDuration(durationDays)}'),
              Text('剩餘時間：${_formatDuration(remainingDays)}'),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // 度數資訊
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('界起始度數：${startDegree.toStringAsFixed(2)}°'),
              Text('界結束度數：${endDegree.toStringAsFixed(2)}°'),
              Text('相差度數：${degreeDifference.toStringAsFixed(2)}°'),
              Text('每度需要時間：${_formatDuration(timePerDegree)}'),
            ],
          ),
        ),
      ],
    );
  }

  /// 建立界主星項目
  Widget _buildTermItem(BuildContext context, Map<String, dynamic> term) {
    final isCurrentTerm = term['isCurrentTerm'] as bool;
    final termRulerName = term['termRulerName'] as String;
    final startDateTime = term['startDateTime'] as DateTime;
    final endDateTime = term['endDateTime'] as DateTime;
    final durationDays = term['durationDays'] as double;
    final sign = term['sign'] as String;
    final startDegree = term['startDegree'] as double;
    final endDegree = term['endDegree'] as double;

    // 檢查是否為星座切換
    final isSignChange = term['isSignChange'] as bool? ?? false;

    // 計算星座內的度數
    final startDegreeInSign = startDegree % 30.0;
    final endDegreeInSign = endDegree % 30.0;

    // 星座顏色映射
    final signColors = {
      '牡羊座': Colors.red,
      '金牛座': Colors.green,
      '雙子座': Colors.yellow[700],
      '巨蟹座': Colors.blue[300],
      '獅子座': Colors.orange,
      '處女座': Colors.brown,
      '天秤座': Colors.pink,
      '天蠍座': Colors.purple,
      '射手座': Colors.indigo,
      '摩羯座': Colors.grey[700],
      '水瓶座': Colors.cyan,
      '雙魚座': Colors.teal,
    };

    final signColor = signColors[sign] ?? Colors.grey;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCurrentTerm
            ? Colors.blue.withOpacity(0.1)
            : isSignChange
                ? signColor.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
        border: Border.all(
          color: isCurrentTerm
              ? Colors.blue
              : isSignChange
                  ? signColor
                  : Colors.grey.withOpacity(0.3),
          width: isCurrentTerm
              ? 2
              : isSignChange
                  ? 1.5
                  : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                termRulerName,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isCurrentTerm ? Colors.blue : null,
                ),
              ),
              const SizedBox(width: 8),
              if (isCurrentTerm)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    '當前',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  ),
                ),
              if (sign.isNotEmpty) ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: signColor.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    sign,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
              if (isSignChange) ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    '星座切換',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 4),

          // 星座切換資訊顯示
          if (term['signTotalTime'] != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                border: Border.all(color: Colors.purple.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$termRulerName$sign星座切換',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$sign星座時間：${_formatDualTime(term['signTotalTime'] as double)}',
                    style: const TextStyle(fontSize: 11),
                  ),
                  Text(
                    '$sign星座一度所需時間：${_formatSignTimePerDegree(term['signTotalTime'] as double)}',
                    style: const TextStyle(fontSize: 11),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
          ],

          Text(
            '開始：${_formatDateTimeWithSign(startDateTime, sign, startDegreeInSign)}',
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            '結束：${_formatDateTimeWithSign(endDateTime, sign, endDegreeInSign)}',
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            '持續：${_formatDualTime(durationDays)}',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isCurrentTerm ? Colors.blue[700] : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  /// 根據圖表類型決定使用哪種繪製器
  CustomPainter _getChartPainter() {
    print('_getChartPainter: 創建新的 CustomPainter 實例');
    final chartType = widget.viewModel.chartType;

    // 如果是法達盤，使用 FirdariaChartPainter
    if (chartType == ChartType.firdaria) {
      // 取得法達盤數據
      final firdariaData = widget.viewModel.firdariaData ?? [];
      final birthDate = widget.viewModel.chartData.primaryPerson.birthDate;
      final isDaytime = widget.viewModel.isDaytimeBirth;

      _cachedPainter = FirdariaChartPainter(
        widget.viewModel.chartData.planets!, // 行星數據
        widget.viewModel.chartData.aspects!,
        housesData: widget.viewModel.chartData.houses!,
        chartType: chartType,
        firdariaData: firdariaData,
        birthDate: birthDate,
        isDaytime: isDaytime,
        selectedPeriodIndex: widget.viewModel.selectedFirdariaPeriodIndex,
      );

      return _cachedPainter!;
    }

    // 需要雙圈顯示的圖表類型
    if (_needsDualChartPainter()) {
      // 根據不同的圖表類型選擇適當的行星數據
      if (chartType == ChartType.synastry ||
          chartType == ChartType.synastrySecondary ||
          chartType == ChartType.synastryTertiary) {
        // 比較盤：本命盤和次要人物的行星
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
          widget.viewModel.chartData.secondaryPerson!.planets!, // 次要人物行星
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
        );
      } else if (chartType == ChartType.transit) {
        // 行運盤：本命盤和當前行運行星
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
          widget.viewModel.chartData.planets!, // 行運盤行星
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
        );
      } else {
        // 其他雙圈圖表：使用相同的行星數據
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!, // 本命盤行星
          widget.viewModel.chartData.planets!, // 行運盤行星
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
        );
      }
    } else {
      _cachedPainter = ChartPainter(
        widget.viewModel.chartData.planets!, // 行星數據
        widget.viewModel.chartData.aspects!,
        housesData: widget.viewModel.chartData.houses!,
        chartType: chartType,
        chartSettings: widget.viewModel.chartSettings, // 傳入星盤設定
      );
    }

    return _cachedPainter!;
  }

  /// 判斷是否需要使用雙圈星盤繪製器
  bool _needsDualChartPainter() {
    final chartType = widget.viewModel.chartType;

    // 預測類星盤中的特定類型
    final progressionTypes = [
      ChartType.solarArcDirection,
    ];

    // 需要雙圈顯示的特殊類型
    final specialDualTypes = [
      ChartType.transit,
      ChartType.synastry,
    ];

    return progressionTypes.contains(chartType) ||
        specialDualTypes.contains(chartType) ||
        chartType.isSynastryProgression;
  }

  /// 獲取圖表標題
  String _getChartTitle() {
    switch (widget.viewModel.chartType) {
      case ChartType.equinoxSolstice:
        return widget.viewModel.chartData.primaryPerson.name;
      case ChartType.mundane:
      case ChartType.horary:
        return widget.viewModel.chartType.name;
      default:
        break;
    }
    if (widget.viewModel.chartType.requiresTwoPersons &&
        widget.viewModel.chartData.secondaryPerson != null) {
      return '${widget.viewModel.chartData.primaryPerson.name} 與 ${widget.viewModel.chartData.secondaryPerson!.name} 的${widget.viewModel.chartType.name}';
    } else {
      return '${widget.viewModel.chartData.primaryPerson.name}的${widget.viewModel.chartType.name}';
    }
  }

  /// 處理星盤點擊事件
  void _handleChartTap(
      BuildContext context, Offset tapPosition, double chartSize) {
    print('點擊位置: $tapPosition');

    // 檢查點擊的是哪個行星
    final CustomPainter painter = _getChartPainter();
    print('繪製器類型: ${painter.runtimeType}');

    // 先調用 hitTest 方法進行點擊檢測
    bool? hitResult = painter.hitTest(tapPosition);
    print('點擊結果: $hitResult');

    // 如果是法達盤，處理法達盤的點擊事件
    if (painter is FirdariaChartPainter) {
      final hitPeriod = painter.getLastHitPeriod();
      if (hitPeriod != null) {
        print('點中的法達盤週期: ${hitPeriod.majorPlanetName}');
        // 找到點擊的週期索引
        final index = widget.viewModel.firdariaData?.indexOf(hitPeriod) ?? -1;
        if (index >= 0) {
          // 更新選中的週期索引
          widget.viewModel.selectedFirdariaPeriodIndex = index;
          // 刷新頁面
          setState(() {});
        }
        return;
      }
    }

    PlanetPosition? hitPlanet;

    // 如果是 ChartPainter 類型，取得點擊的行星
    if (painter is ChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
      print('點中的行星 (ChartPainter): ${hitPlanet?.name}');
    }
    // 如果是 DualChartPainter 類型，取得點擊的行星
    else if (painter is DualChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
      print('點中的行星 (DualChartPainter): ${hitPlanet?.name}');
    }
    // 如果是 FirdariaChartPainter 類型，取得點擊的行星
    else if (painter is FirdariaChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
      print('點中的行星 (FirdariaChartPainter): ${hitPlanet?.name}');
    }

    // 如果找到行星，顯示其詳細信息
    if (hitPlanet != null) {
      print('顯示行星詳情: ${hitPlanet.name}');
      _showPlanetDetails(context, hitPlanet);
    } else {
      print('未找到行星或法達盤週期');
    }
  }

  /// 顯示行星詳細信息
  void _showPlanetDetails(BuildContext context, PlanetPosition planet) {
    print('_showPlanetDetails: planet = ${planet.name}');

    // 先移除現有的 overlay
    _removeOverlay();

    // 計算行星在畫面上的位置
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    print('_showPlanetDetails: size = $size');

    // 獲取行星相位信息
    final aspects = widget.viewModel.chartData.aspects
            ?.where((aspect) =>
                (aspect.planet1.name == planet.name ||
                    aspect.planet2.name == planet.name) &&
                aspect.receptionType == ReceptionType.none)
            .toList() ??
        [];
    print('_showPlanetDetails: aspects.length = ${aspects.length}');

    // 獲取行星互容接納關係
    final receptions = widget.viewModel.chartData.aspects
            ?.where((aspect) =>
                (aspect.planet1.name == planet.name ||
                    aspect.planet2.name == planet.name) &&
                aspect.receptionType != ReceptionType.none)
            .toList() ??
        [];
    print('_showPlanetDetails: receptions.length = ${receptions.length}');

    // 計算卡片的位置，確保它不會超出畫面
    const double cardWidth = 280;
    const double cardHeight = 300; // 估計高度

    // 先將卡片放在畫面中央
    double left = (size.width - cardWidth) / 2;
    double top = (size.height - cardHeight) / 2;
    print('_showPlanetDetails: card position = ($left, $top)');

    try {
      // 創建並插入 overlay
      _overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            // 透明全屏底層，用於捕捉點擊事件
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeOverlay, // 點擊空白處關閉卡片
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
            // 可拖動卡片
            DraggablePlanetDetailCard(
              planet: planet,
              aspects: aspects,
              receptions: receptions,
              viewModel: widget.viewModel,
              onClose: _removeOverlay,
              initialPosition: Offset(left, top),
            ),
          ],
        ),
      );

      // 插入 overlay
      Overlay.of(context).insert(_overlayEntry!);
      print('_showPlanetDetails: overlay inserted');

      // 更新最後點擊的行星
      setState(() {
        _lastHitPlanet = planet;
      });
    } catch (e) {
      print('_showPlanetDetails error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.viewModel.chartData.planets == null ||
        widget.viewModel.chartData.houses == null) {
      return const Center(child: CircularProgressIndicator());
    }
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    const tabBarHeight = 48.0; // TabBar 的標準高度
    final topPadding = MediaQuery.of(context).padding.top;
    final availableHeight =
        screenHeight - appBarHeight - tabBarHeight - topPadding - 20;
    final chartSize = availableHeight * 0.6; // 使用可用高度的 60%

    // 使用統一的佈局結構
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 10),
          Text(
            _getChartTitle(),
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),

          // 星盤圖
          SizedBox(
            height: chartSize,
            width: chartSize,
            child: GestureDetector(
              onTapDown: (TapDownDetails details) {
                _handleChartTap(context, details.localPosition, chartSize);
              },
              child: CustomPaint(
                painter: _getChartPainter(),
              ),
            ),
          ),

          const SizedBox(height: 10),

          // 時間調整控制元素
          TimeAdjustmentWidget(viewModel: widget.viewModel),

          const SizedBox(height: 10),

          // 界主星配置法按鈕
          ElevatedButton(
            onPressed: () => _showTermRulerProgression(context),
            child: const Text('界主星配置法'),
          ),

          const SizedBox(height: 10),

          // 使用模組化的人物信息組件，放在底部
          PersonInfoWidget(viewModel: widget.viewModel),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  /// 構建信息卡片
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片標題
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          // 卡片內容
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: valueColor ?? Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 可拖動的透明對話框組件
class _DraggableTransparentDialog extends StatefulWidget {
  final Widget child;
  final double maxWidth;

  const _DraggableTransparentDialog({
    Key? key,
    required this.child,
    required this.maxWidth,
  }) : super(key: key);

  @override
  State<_DraggableTransparentDialog> createState() => _DraggableTransparentDialogState();
}

class _DraggableTransparentDialogState extends State<_DraggableTransparentDialog> {
  Offset _position = Offset.zero;
  bool _isDragging = false;
  double _opacity = 1.0;

  @override
  void initState() {
    super.initState();
    // 初始位置設置為螢幕中央
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenSize = MediaQuery.of(context).size;
      setState(() {
        _position = Offset(
          (screenSize.width - widget.maxWidth) / 2,
          screenSize.height * 0.1, // 距離頂部 10%
        );
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        // 透明背景，點擊時關閉對話框
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            width: screenSize.width,
            height: screenSize.height,
            color: Colors.transparent,
          ),
        ),
        // 可拖動的對話框
        Positioned(
          left: _position.dx,
          top: _position.dy,
          child: GestureDetector(
            onPanStart: (details) {
              setState(() {
                _isDragging = true;
                _opacity = 0.7; // 拖動時變透明
              });
            },
            onPanUpdate: (details) {
              setState(() {
                _position += details.delta;

                // 限制在螢幕範圍內
                _position = Offset(
                  _position.dx.clamp(0, screenSize.width - widget.maxWidth),
                  _position.dy.clamp(0, screenSize.height - 100), // 保留底部空間
                );
              });
            },
            onPanEnd: (details) {
              setState(() {
                _isDragging = false;
                _opacity = 1.0; // 停止拖動時恢復不透明
              });
            },
            onTap: () {
              // 點擊對話框內容時切換透明度
              setState(() {
                _opacity = _opacity == 1.0 ? 0.3 : 1.0;
              });
            },
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _opacity,
              child: Container(
                width: widget.maxWidth,
                constraints: BoxConstraints(
                  maxHeight: screenSize.height * 0.8,
                ),
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Material(
                  borderRadius: BorderRadius.circular(12),
                  elevation: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _isDragging
                          ? Colors.blue.withOpacity(0.5)
                          : Colors.grey.withOpacity(0.3),
                        width: _isDragging ? 2 : 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        // 拖動提示（僅在第一次顯示時出現）
        if (!_isDragging && _position == Offset.zero)
          Positioned(
            right: 10, // 縮小右邊距
            top: 10, // 縮小上邊距
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 縮小內邊距
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(15), // 縮小圓角
              ),
              child: const Text(
                '可拖動 • 點擊切換透明度',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10, // 縮小字體
                ),
              ),
            ),
          ),
      ],
    );
  }
}
