import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../models/aspect_info.dart';
import '../models/birth_data.dart';
import '../models/chart_data.dart';
import '../models/chart_type.dart';
import '../models/divination_record.dart';
import '../models/planet_position.dart';
import '../services/divination_record_service.dart';
import '../utils/astrology_calculator.dart' as astro;
import '../utils/geocoding_service.dart';
import '../utils/logger_utils.dart';

/// 時刻盤視圖模型
/// 用於處理占星卜卦相關的業務邏輯
class HoraryChartViewModel extends ChangeNotifier {
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  String? _errorMessage;

  String? get errorMessage => _errorMessage;

  // 卜卦結果
  ChartData? _chartData;

  ChartData? get chartData => _chartData;

  // 設置加載狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 設置錯誤訊息
  void setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// 進行占星卜卦分析
  Future<void> performHoraryDivination({
    required DateTime questionTime,
    required String location,
    required String question,
  }) async {
    setLoading(true);
    setErrorMessage(null);

    try {
      // 從地址獲取經緯度
      final coordinates =
          await GeocodingService.getCoordinatesFromAddress(location);
      final latitude = coordinates['latitude']!;
      final longitude = coordinates['longitude']!;

      // 創建卜卦時間的星盤數據
      final chartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: BirthData(
          id: 'horary_${questionTime.millisecondsSinceEpoch}',
          name: '占星卜卦',
          birthDate: questionTime,
          birthPlace: location,
          latitude: latitude,
          longitude: longitude,
          notes: question,
        ),
      );

      // 計算行星位置
      _chartData =
          await astro.AstrologyCalculator.calculatePlanetPositionsForChart(
        chartData,
      );

      // 保存卜卦記錄
      await _saveHoraryRecord(question, location, questionTime);

      setLoading(false);
      notifyListeners();
    } catch (e) {
      logger.e('占星卜卦分析時出錯: $e');
      setLoading(false);
      setErrorMessage('占星卜卦分析時出錯: $e');
    }
  }

  /// 獲取卜卦結果的文本描述
  /// 參考 ChartViewModel.generateChartInfoText 的格式
  String getHoraryResultText() {
    if (_chartData == null) {
      return '尚未進行卜卦';
    }

    final StringBuffer buffer = StringBuffer();
    final birthData = _chartData!.primaryPerson;
    final planets = _chartData!.planets ?? [];
    final aspects = _chartData!.aspects ?? [];
    final houses = _chartData!.houses;

    // 標題和基本信息
    buffer.writeln('=' * 50);
    buffer.writeln('占星卜卦結果');
    buffer.writeln('=' * 50);
    buffer.writeln();

    // 卜卦問題
    buffer.writeln('【卜卦問題】');
    buffer.writeln(birthData.notes ?? '未記錄問題');
    buffer.writeln();

    // 基本資料
    buffer.writeln('【基本資料】');
    buffer.writeln('時間: ${_formatDateTime(birthData.birthDate)}');
    buffer.writeln('地點: ${birthData.birthPlace}');
    buffer.writeln();

    // 上升點和重要軸點
    buffer.writeln('【重要軸點】');
    PlanetPosition? ascendant;
    PlanetPosition? mc;
    PlanetPosition? descendant;
    PlanetPosition? ic;

    for (final planet in planets) {
      switch (planet.name) {
        case '上升':
          ascendant = planet;
          break;
        case '中天':
          mc = planet;
          break;
        case '下降':
          descendant = planet;
          break;
        case '天底':
          ic = planet;
          break;
      }
    }

    if (ascendant != null) {
      buffer.writeln('上升點: ${ascendant.sign} ${_formatDegreeMinute(ascendant.longitude)}');
    }
    if (mc != null) {
      buffer.writeln('中天: ${mc.sign} ${_formatDegreeMinute(mc.longitude)}');
    }
    if (descendant != null) {
      buffer.writeln('下降點: ${descendant.sign} ${_formatDegreeMinute(descendant.longitude)}');
    }
    if (ic != null) {
      buffer.writeln('天底: ${ic.sign} ${_formatDegreeMinute(ic.longitude)}');
    }
    buffer.writeln();

    // 主要行星位置（包含更多詳細信息）
    buffer.writeln('【行星位置】');
    final mainPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星'];

    for (final planetName in mainPlanets) {
      final planet = planets.where((p) => p.name == planetName).firstOrNull;
      if (planet != null) {
        final signDegree = planet.longitude % 30;
        StringBuffer planetInfo = StringBuffer();

        // 基本位置信息
        planetInfo.write('$planetName: ${planet.sign} ${_formatDegreeMinute(signDegree)}, 第${planet.house}宮');

        // 添加逆行信息
        if (planet.longitudeSpeed < 0) {
          planetInfo.write(' (逆行)');
        }

        // 添加尊貴力量信息（如果有的話）
        if (planet.dignity.toString() != 'PlanetDignity.normal') {
          final dignityText = _getDignityText(planet);
          if (dignityText.isNotEmpty) {
            planetInfo.write(' ($dignityText)');
          }
        }

        buffer.writeln(planetInfo.toString());
      }
    }
    buffer.writeln();

    // 宮主星信息
    if (houses != null) {
      buffer.writeln('【宮主星】');
      final houseRulers = _calculateHouseRulers(houses, planets);
      for (int i = 1; i <= 12; i++) {
        final ruler = houseRulers[i];
        if (ruler != null) {
          buffer.writeln('第$i宮主星: $ruler');
        }
      }
      buffer.writeln();
    }

    // 重要相位（更詳細的相位信息）
    buffer.writeln('【重要相位】');
    final significantAspects = aspects.where((a) {
      // 篩選主要相位和主要行星
      final isMainAspect = [0, 60, 90, 120, 180].contains(a.angle.round());
      final isMainPlanet1 = mainPlanets.contains(a.planet1.name) ||
                           ['上升', '中天'].contains(a.planet1.name);
      final isMainPlanet2 = mainPlanets.contains(a.planet2.name) ||
                           ['上升', '中天'].contains(a.planet2.name);
      return isMainAspect && isMainPlanet1 && isMainPlanet2;
    }).take(8).toList();

    if (significantAspects.isEmpty) {
      buffer.writeln('無重要相位');
    } else {
      for (final aspect in significantAspects) {
        // 添加入相或出相的信息
        String directionText = '';
        if (aspect.direction != null) {
          directionText = ' ${aspect.direction == AspectDirection.applying ? '入相' : '出相'}';
        }

        buffer.writeln(
          '${aspect.planet1.name} ${aspect.shortZh} ${aspect.planet2.name} '
          '(容許度: ${aspect.orb.toStringAsFixed(2)}°$directionText)'
        );
      }
    }
    buffer.writeln();

    // 卜卦分析要點
    buffer.writeln('【卜卦分析要點】');
    buffer.writeln('• 問卜者代表: 第1宮及其宮主星');
    buffer.writeln('• 所問事項: 根據問題性質確定相關宮位');
    buffer.writeln('• 月亮: 事件的發展趨勢');
    buffer.writeln('• 相位關係: 事件的結果指向');
    buffer.writeln('• 宮主星狀態: 事件的可能性');
    buffer.writeln();

    // 簡單的卜卦判斷提示
    buffer.writeln('【判斷提示】');
    if (ascendant != null) {
      buffer.writeln('• 上升星座 ${ascendant.sign} 反映問卜時的心境');
    }

    final moon = planets.where((p) => p.name == '月亮').firstOrNull;
    if (moon != null) {
      buffer.writeln('• 月亮在 ${moon.sign} 第${moon.house}宮，顯示事件發展方向');
    }

    // 檢查是否有困難相位
    final difficultAspects = aspects.where((a) =>
      [90, 180].contains(a.angle.round()) &&
      mainPlanets.contains(a.planet1.name) &&
      mainPlanets.contains(a.planet2.name)
    ).length;

    if (difficultAspects > 0) {
      buffer.writeln('• 存在 $difficultAspects 個困難相位，需要謹慎考慮');
    }

    // 檢查是否有和諧相位
    final harmonicAspects = aspects.where((a) =>
      [60, 120].contains(a.angle.round()) &&
      mainPlanets.contains(a.planet1.name) &&
      mainPlanets.contains(a.planet2.name)
    ).length;

    if (harmonicAspects > 0) {
      buffer.writeln('• 存在 $harmonicAspects 個和諧相位，情況較為順利');
    }

    buffer.writeln();
    buffer.writeln('=' * 50);
    buffer.writeln('※ 以上為基本卜卦資訊，具體解讀需結合問題性質進行分析');
    buffer.writeln('=' * 50);

    return buffer.toString();
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化度數分鐘（更精確的格式）
  String _formatDegreeMinute(double longitude) {
    final signDegree = longitude % 30;
    final int degree = signDegree.floor();
    final int minute = ((signDegree - degree) * 60).round();
    return '$degree°$minute\'';
  }

  /// 獲取行星尊貴力量文本
  String _getDignityText(PlanetPosition planet) {
    switch (planet.dignity.toString()) {
      case 'PlanetDignity.domicile':
        return '廟旺';
      case 'PlanetDignity.exaltation':
        return '擢升';
      case 'PlanetDignity.detriment':
        return '失勢';
      case 'PlanetDignity.fall':
        return '落陷';
      default:
        return '';
    }
  }

  /// 計算宮主星
  Map<int, String> _calculateHouseRulers(dynamic houses, List<PlanetPosition> planets) {
    final Map<int, String> houseRulers = {};

    try {
      if (houses.cusps != null && houses.cusps.length >= 12) {
        for (int i = 0; i < 12; i++) {
          final cuspLongitude = houses.cusps[i + 1]; // cusps 從索引 1 開始
          final signIndex = (cuspLongitude / 30).floor() % 12;
          final ruler = _getHouseRulerBySignIndex(signIndex);
          houseRulers[i + 1] = ruler;
        }
      }
    } catch (e) {
      // 如果計算失敗，返回空的 Map
    }

    return houseRulers;
  }

  /// 根據星座索引獲取宮主星
  String _getHouseRulerBySignIndex(int signIndex) {
    const rulers = [
      '火星',    // 白羊座
      '金星',    // 金牛座
      '水星',    // 雙子座
      '月亮',    // 巨蟹座
      '太陽',    // 獅子座
      '水星',    // 處女座
      '金星',    // 天秤座
      '火星',    // 天蠍座
      '木星',    // 射手座
      '土星',    // 摩羯座
      '土星',    // 水瓶座
      '木星',    // 雙魚座
    ];

    if (signIndex >= 0 && signIndex < rulers.length) {
      return rulers[signIndex];
    }
    return '未知';
  }

  /// 保存占星卜卦記錄
  Future<void> _saveHoraryRecord(String question, String location, DateTime timestamp) async {
    try {
      if (_chartData == null) {
        logger.w('無法保存卜卦記錄：無星盤數據');
        return;
      }

      logger.d('開始保存占星卜卦記錄...');

      // 生成記錄ID
      final String recordId = const Uuid().v4();

      // 獲取卜卦結果文本
      final String resultText = getHoraryResultText();
      logger.d('卜卦結果文本長度: ${resultText.length}');

      // 創建卜卦記錄
      final DivinationRecord record = DivinationRecord(
        id: recordId,
        question: question,
        location: location,
        timestamp: timestamp,
        type: 'horary',
        result: resultText,
        chartData: _chartData!.toJson(),
      );

      logger.d('創建記錄完成，正在保存...');

      // 保存記錄
      await DivinationRecordService.saveRecord(record);
      logger.d('占星卜卦記錄已成功保存: $recordId');
    } catch (e) {
      logger.e('保存占星卜卦記錄時出錯: $e');
      logger.e('錯誤堆棧: ${e.toString()}');
    }
  }
}
