import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../models/birth_data.dart';
import '../models/chart_data.dart';
import '../models/chart_settings.dart';
import '../models/chart_type.dart';
import '../models/divination_record.dart';
import '../services/chart_service.dart';
import '../services/divination_record_service.dart';
import '../utils/geocoding_service.dart';
import '../utils/logger_utils.dart';
import 'chart_viewmodel.dart';

/// 時刻盤視圖模型
/// 用於處理占星卜卦相關的業務邏輯
class HoraryChartViewModel extends ChangeNotifier {
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  String? _errorMessage;

  String? get errorMessage => _errorMessage;

  // 卜卦結果
  ChartData? _chartData;

  ChartData? get chartData => _chartData;

  // 設置加載狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // 設置錯誤訊息
  void setErrorMessage(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// 進行占星卜卦分析
  Future<void> performHoraryDivination({
    required DateTime questionTime,
    required String location,
    required String question,
  }) async {
    setLoading(true);
    setErrorMessage(null);

    try {
      // 從地址獲取經緯度
      final coordinates =
          await GeocodingService.getCoordinatesFromAddress(location);
      final latitude = coordinates['latitude']!;
      final longitude = coordinates['longitude']!;

      // 創建卜卦時間的星盤數據
      final chartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: BirthData(
          id: 'horary_${questionTime.millisecondsSinceEpoch}',
          name: '占星卜卦',
          birthDate: questionTime,
          birthPlace: location,
          latitude: latitude,
          longitude: longitude,
          notes: question,
        ),
      );
      ChartSettings? chartSettings = await ChartSettings.loadFromPrefs();
      logger.d('已加載用戶星盤設定');

      // 使用 ChartService 計算行星位置、宮位和相位，並傳遞行星可見性設定和相位容許度設定
      _chartData = await ChartService().calculatePlanetPositionsForChart(
        chartData,
        planetVisibility: chartSettings.planetVisibility,
        aspectOrbs: chartSettings.aspectOrbs,
      );

      // 保存卜卦記錄
      await _saveHoraryRecord(question, location, questionTime);

      setLoading(false);
      notifyListeners();
    } catch (e) {
      logger.e('占星卜卦分析時出錯: $e');
      setLoading(false);
      setErrorMessage('占星卜卦分析時出錯: $e');
    }
  }

  /// 獲取卜卦結果的文本描述
  /// 使用 ChartViewModel.generateChartInfoText 方法
  Future<String> getHoraryResultText() async {
    if (_chartData == null) {
      return '尚未進行卜卦';
    }

    // 創建 ChartViewModel 實例來使用其 generateChartInfoText 方法
    final chartViewModel = ChartViewModel.withChartData(initialChartData: _chartData!);

    // 獲取基本的星盤信息文本
    final basicChartInfo = await chartViewModel.generateChartInfoText();

    // 添加卜卦特有的信息
    final StringBuffer buffer = StringBuffer();
    final birthData = _chartData!.primaryPerson;

    // 卜卦標題
    buffer.writeln('=' * 50);
    buffer.writeln('占星卜卦結果');
    buffer.writeln('=' * 50);
    // buffer.writeln();

    // // 卜卦問題
    // buffer.writeln('【卜卦問題】');
    // buffer.writeln(birthData.notes ?? '未記錄問題');
    // buffer.writeln();

    // 插入基本星盤信息
    buffer.writeln(basicChartInfo);
    // buffer.writeln();

    // 添加卜卦特有的分析要點
    buffer.writeln('【卜卦分析要點】');
    buffer.writeln('• 問卜者代表: 第1宮及其宮主星');
    buffer.writeln('• 所問事項: 根據問題性質確定相關宮位');
    buffer.writeln('• 月亮: 事件的發展趨勢和時間指示');
    buffer.writeln('• 相位關係: 事件的結果指向');
    buffer.writeln('• 宮主星狀態: 事件的可能性和發展');
    buffer.writeln();

    // 卜卦判斷提示
    buffer.writeln('【卜卦判斷提示】');

    final planets = _chartData!.planets ?? [];
    final aspects = _chartData!.aspects ?? [];

    // 上升點分析
    final ascendant = planets.where((p) => p.name == '上升').firstOrNull;
    if (ascendant != null) {
      buffer.writeln('• 上升星座 ${ascendant.sign} 反映問卜時的心境和環境');
    }

    // 月亮分析
    final moon = planets.where((p) => p.name == '月亮').firstOrNull;
    if (moon != null) {
      buffer.writeln('• 月亮在 ${moon.sign} 第${moon.house}宮，顯示事件發展方向');
      if (moon.longitudeSpeed < 0) {
        buffer.writeln('• 月亮逆行，事件可能有延遲或需要重新考慮');
      }
    }

    // 相位分析
    final mainPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星'];
    final difficultAspects = aspects.where((a) =>
      [90, 180].contains(a.angle.round()) &&
      mainPlanets.contains(a.planet1.name) &&
      mainPlanets.contains(a.planet2.name)
    ).length;

    final harmonicAspects = aspects.where((a) =>
      [60, 120].contains(a.angle.round()) &&
      mainPlanets.contains(a.planet1.name) &&
      mainPlanets.contains(a.planet2.name)
    ).length;

    if (difficultAspects > 0) {
      buffer.writeln('• 存在 $difficultAspects 個困難相位，需要謹慎考慮障礙');
    }

    if (harmonicAspects > 0) {
      buffer.writeln('• 存在 $harmonicAspects 個和諧相位，情況較為順利');
    }

    // 第1宮和第7宮分析（問卜者和對方）
    final firstHousePlanets = planets.where((p) => p.house == 1).toList();
    final seventhHousePlanets = planets.where((p) => p.house == 7).toList();

    if (firstHousePlanets.isNotEmpty) {
      buffer.writeln('• 第1宮有行星：${firstHousePlanets.map((p) => p.name).join('、')}，影響問卜者狀態');
    }

    if (seventhHousePlanets.isNotEmpty) {
      buffer.writeln('• 第7宮有行星：${seventhHousePlanets.map((p) => p.name).join('、')}，影響對方或合作關係');
    }

    buffer.writeln();
    buffer.writeln('=' * 50);
    buffer.writeln('※ 以上為基本卜卦資訊，具體解讀需結合問題性質和傳統卜卦規則進行分析');
    buffer.writeln('※ 建議諮詢專業占星師進行詳細解讀');
    buffer.writeln('=' * 50);

    return buffer.toString();
  }

  /// 設置測試用的星盤數據（僅用於測試）
  void setTestChartData(ChartData chartData) {
    _chartData = chartData;
    notifyListeners();
  }

  /// 保存占星卜卦記錄
  Future<void> _saveHoraryRecord(String question, String location, DateTime timestamp) async {
    try {
      if (_chartData == null) {
        logger.w('無法保存卜卦記錄：無星盤數據');
        return;
      }

      logger.d('開始保存占星卜卦記錄...');

      // 生成記錄ID
      final String recordId = const Uuid().v4();

      // 獲取卜卦結果文本
      final String resultText = await getHoraryResultText();
      logger.d('卜卦結果文本長度: ${resultText.length}');

      // 創建卜卦記錄
      final DivinationRecord record = DivinationRecord(
        id: recordId,
        question: question,
        location: location,
        timestamp: timestamp,
        type: 'horary',
        result: resultText,
        chartData: _chartData!.toJson(),
      );

      logger.d('創建記錄完成，正在保存...');

      // 保存記錄
      await DivinationRecordService.saveRecord(record);
      logger.d('占星卜卦記錄已成功保存: $recordId');
    } catch (e) {
      logger.e('保存占星卜卦記錄時出錯: $e');
      logger.e('錯誤堆棧: ${e.toString()}');
    }
  }
}
