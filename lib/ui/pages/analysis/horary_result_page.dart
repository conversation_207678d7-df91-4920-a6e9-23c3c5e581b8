import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../../models/chart_data.dart';
import '../../../viewmodels/chart_viewmodel.dart';
import '../../../viewmodels/horary_viewmodel.dart';
import '../../AppTheme.dart';
import '../../dialogs/copy_options_dialog.dart';
import '../chart_page.dart';

/// 占星卜卦結果頁面
/// 專門用於顯示卜卦結果，不包含星盤圖與行星位置的tab
class HoraryResultPage extends StatefulWidget {
  final String question;
  final ChartData chartData;

  const HoraryResultPage({
    Key? key,
    required this.question,
    required this.chartData,
  }) : super(key: key);

  @override
  State<HoraryResultPage> createState() => _HoraryResultPageState();
}

class _HoraryResultPageState extends State<HoraryResultPage> {
  late HoraryViewModel _horaryViewModel;

  @override
  void initState() {
    super.initState();
    _horaryViewModel = Provider.of<HoraryViewModel>(context, listen: false);
  }

  /// 複製卜卦結果（使用星盤頁面的複製功能）
  Future<void> _copyHoraryResultWithOptions() async {
    try {
      // 創建 ChartViewModel 來使用複製功能
      final chartViewModel = ChartViewModel.withChartData(initialChartData: widget.chartData);
      
      // 顯示複製選項對話框
      final options = await showCopyOptionsDialog(context);
      
      // 如果用戶選擇了選項（沒有取消）
      if (options != null && mounted) {
        // 使用 ChartViewModel 的複製功能
        final success = await chartViewModel.copyChartInfo(options: options);
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('卜卦結果已複製到剪貼板'),
              duration: Duration(seconds: 2),
            ),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('複製失敗，請重試'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('複製資訊時出錯: $e')),
        );
      }
    }
  }

  /// 複製簡單的卜卦結果文本
  void _copySimpleHoraryResult() {
    try {
      final text = _horaryViewModel.getHoraryResultText();

      // 複製到剪貼板
      Clipboard.setData(ClipboardData(text: text));

      // 顯示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('卜卦結果已複製到剪貼板'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // 顯示錯誤提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('複製資訊時出錯: $e')),
      );
    }
  }

  /// 查看完整星盤
  void _viewFullChart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: widget.chartData),
          child: ChartPage(chartData: widget.chartData),
        ),
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final birthData = widget.chartData.primaryPerson;

    return Scaffold(
      appBar: AppBar(
        title: const Text('卜卦結果'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          // 複製選項按鈕
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            tooltip: '複製選項',
            onSelected: (String action) async {
              switch (action) {
                case 'copy_simple':
                  _copySimpleHoraryResult();
                  break;
                case 'copy_detailed':
                  await _copyHoraryResultWithOptions();
                  break;
                case 'view_chart':
                  _viewFullChart();
                  break;
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'copy_simple',
                child: Row(
                  children: [
                    Icon(Icons.content_copy, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('複製卜卦結果'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'copy_detailed',
                child: Row(
                  children: [
                    Icon(Icons.copy_all, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('詳細複製選項'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'view_chart',
                child: Row(
                  children: [
                    Icon(Icons.auto_awesome, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('查看完整星盤'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 問題信息卡片
            _buildQuestionCard(),
            const SizedBox(height: 16),
            
            // 卜卦結果內容
            _buildResultContent(),
            
            const SizedBox(height: 16),
            
            // 快速操作按鈕
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  /// 構建問題信息卡片
  Widget _buildQuestionCard() {
    final birthData = widget.chartData.primaryPerson;
    
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.1),
              AppColors.solarAmber.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.auto_awesome, color: AppColors.royalIndigo),
                const SizedBox(width: 8),
                const Text(
                  '卜卦問題',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              widget.question.isNotEmpty ? widget.question : (birthData.notes ?? '未記錄問題'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  '時間：${_formatDateTime(birthData.birthDate)}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  '地點：${birthData.birthPlace}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建結果內容
  Widget _buildResultContent() {
    return Consumer<HoraryViewModel>(
      builder: (context, viewModel, child) {
        final resultText = viewModel.getHoraryResultText();
        
        return Card(
          elevation: 3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.psychology, color: AppColors.solarAmber),
                    const SizedBox(width: 8),
                    const Text(
                      '卜卦結果',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.solarAmber,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    resultText.isNotEmpty ? resultText : '暫無卜卦結果',
                    style: const TextStyle(
                      fontSize: 15,
                      height: 1.6,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 構建快速操作按鈕
  Widget _buildQuickActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.content_copy, size: 18),
            label: const Text('複製結果'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.solarAmber,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: _copySimpleHoraryResult,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.auto_awesome, size: 18),
            label: const Text('查看星盤'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: _viewFullChart,
          ),
        ),
      ],
    );
  }
}
