import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../ui/AppTheme.dart';
import '../../../viewmodels/horary_chart_viewmodel.dart';
import '../../../widgets/styled_card.dart';
import 'divination_records_page.dart';
import 'horary_result_page.dart';

class DivinationAnalysisPage extends StatefulWidget {
  final String title;
  final String? description;

  const DivinationAnalysisPage({super.key, required this.title, this.description});

  @override
  State<DivinationAnalysisPage> createState() => _DivinationAnalysisPageState();
}

class _DivinationAnalysisPageState extends State<DivinationAnalysisPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _questionController = TextEditingController();
  final _locationController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  DateTime _selectedDateTime = DateTime.now();
  bool _isPerformingDivination = false;
  bool _showChartResult = false;

  // 占星卜卦 ViewModel
  late HoraryChartViewModel _horaryViewModel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _locationController.text = '台北市'; // 預設地點
    _horaryViewModel = HoraryChartViewModel();
  }

  /// 選擇日期時間
  Future<void> _selectDateTime() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
      );

      if (pickedTime != null) {
        setState(() {
          _selectedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
        });
      }
    }
  }

  /// 提交占星卜卦
  Future<void> _submitHoraryChart() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isPerformingDivination = true;
    });

    // 顯示加載指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );

    try {
      await _horaryViewModel.performHoraryDivination(
        questionTime: _selectedDateTime,
        location: _locationController.text,
        question: _questionController.text,
      );

      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示星盤結果
      if (mounted && _horaryViewModel.chartData != null) {
        setState(() {
          _showChartResult = true;
        });
      }
    } catch (e) {
      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('卜卦時出錯: $e')),
        );
      }
    } finally {
      setState(() {
        _isPerformingDivination = false;
      });
    }
  }

  /// 執行周易卜卦
  Future<void> _performDivination() async {
    final question = _questionController.text.trim();
    if (question.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請輸入您的問題')),
      );
      return;
    }

    setState(() {
      _isPerformingDivination = true;
    });

    try {
      // 這裡只是一個空的卜卦功能，實際實現時可以添加周易卜卦的邏輯
      await Future.delayed(const Duration(seconds: 2)); // 模擬卜卦過程

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('周易卜卦功能正在開發中')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('卜卦時出錯: $e')),
        );
      }
    } finally {
      setState(() {
        _isPerformingDivination = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _questionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }

  /// 獲取當前位置
  Future<void> _getCurrentLocation() async {
    try {
      // 顯示加載指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      // 檢查位置權限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // 如果用戶拒絕了權限請求，則顯示提示
          if (mounted) {
            Navigator.of(context).pop(); // 關閉加載指示器
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('無法獲取位置權限')),
            );
          }
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // 如果用戶永久拒絕了權限請求，則顯示提示
        if (mounted) {
          Navigator.of(context).pop(); // 關閉加載指示器
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('位置權限被永久拒絕，請在設定中開啟')),
          );
        }
        return;
      }

      // 獲取當前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // 使用 geocoding 套件將經緯度轉換為地址
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 如果有地址信息，則更新地點輸入欄位
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        String address = '';

        // 根據不同的地區格式化地址
        if (place.locality != null && place.locality!.isNotEmpty) {
          address = place.locality!; // 城市
        } else if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          address = place.administrativeArea!; // 省/州
        }

        // 如果地址為空，則使用經緯度
        if (address.isEmpty) {
          address = '${position.latitude}, ${position.longitude}';
        }

        // 更新地點輸入欄位
        setState(() {
          _locationController.text = address;
        });
      } else {
        // 如果沒有地址信息，則使用經緯度
        setState(() {
          _locationController.text = '${position.latitude}, ${position.longitude}';
        });
      }
    } catch (e) {
      print('獲取位置時出錯: $e');
      // 關閉加載指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('獲取位置時出錯: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          // 查看卜卦記錄按鈕
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: '查看卜卦記錄',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DivinationRecordsPage(),
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.solarAmber,
          unselectedLabelColor: Colors.white,
          indicatorColor: AppColors.solarAmber,
          indicatorWeight: 3,
          isScrollable: true,
          tabs: const [
            Tab(text: '占星卜卦'),
            Tab(text: '周易卜卦'),
            Tab(text: '塔羅牌占卜'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildZodiacFortune(),
          _buildYijingDivination(),
          _buildTarotDivination(),
        ],
      ),
    );
  }

  Widget _buildYijingDivination() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // _buildSectionTitle('周易卜卦', Icons.change_history),
          // const SizedBox(height: 16),
          _buildDivinationCard(
            '周易卜卦',
            '通過周易六十四卦解讀您的問題',
            Icons.change_history_outlined,
            content: '周易卜卦是中國古代的一種占卜方式，通過生成六爻卦象來解讀問題。\n'
                '請在心中默念您的問題，然後點擊「開始卜卦」按鈕。系統將隨機生成一個卦象及其變卦，並提供相應的解釋。\n'
                '卜卦結果僅供參考，最終決定權仍在您手中。',
          ),
          // const SizedBox(height: 10),
          Card(
            elevation: 3,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '您的問題',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _questionController,
                    decoration: const InputDecoration(
                      hintText: '請輸入您想要解答的問題...',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _isPerformingDivination ? null : _performDivination,
                      icon: const Icon(Icons.auto_awesome),
                      label: Text(_isPerformingDivination ? '卜卦中...' : '開始卜卦'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.royalIndigo,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 10),
          _buildDivinationTips(),
        ],
      ),
    );
  }

  Widget _buildTarotDivination() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.construction, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            '塔羅牌占卜功能正在開發中',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            '敬請期待',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildZodiacFortune() {
    return ChangeNotifierProvider.value(
      value: _horaryViewModel,
      child: _showChartResult ? _buildChartResultView() : _buildHoraryInputForm(),
    );
  }

  Widget _buildHoraryInputForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // _buildSectionTitle('占星卜卦', Icons.auto_awesome),
            // const SizedBox(height: 16),
            _buildDivinationCard(
              '占星卜卦',
              '通過時刻盤解讀您的問題',
              Icons.auto_awesome,
              content: '占星卜卦（時刻盤）是西方占星學中的一種古老技術，用於回答特定問題。當您提出問題的那一刻，天空中的星象配置被認為與您的問題有著神秘的聯繫。\n'
                  '請在下方輸入您的問題、提問時間和地點，然後點擊「開始卜卦」按鈕。系統將生成時刻盤，並直接顯示星盤資訊和相關解釋。',
            ),
            // const SizedBox(height: 10),
            StyledCard(
              elevation: 3,
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '卜卦資訊',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 問題輸入
                    TextFormField(
                      controller: _questionController,
                      decoration: const InputDecoration(
                        labelText: '問題',
                        hintText: '輸入想要解答的問題...',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '輸入問題';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 日期時間選擇
                    InkWell(
                      onTap: _selectDateTime,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: '提問時間',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          _formatDateTime(_selectedDateTime),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 地點輸入
                    TextFormField(
                      controller: _locationController,
                      decoration: InputDecoration(
                        labelText: '提問地點',
                        hintText: '輸入提問時所在的地點',
                        border: const OutlineInputBorder(),
                        suffixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // 清除按鈕
                            IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _locationController.clear();
                                });
                              },
                              tooltip: '清除',
                            ),
                            // 獲取當前位置按鈕
                            IconButton(
                              icon: const Icon(Icons.my_location),
                              onPressed: _getCurrentLocation,
                              tooltip: '獲取當前位置',
                            ),
                          ],
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '請輸入地點';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // 提交按鈕
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: _isPerformingDivination ? null : _submitHoraryChart,
                        icon: const Icon(Icons.auto_awesome),
                        label: Text(_isPerformingDivination ? '卜卦中...' : '開始卜卦'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.royalIndigo,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            _buildDivinationTips(),
          ],
        ),
      ),
    );
  }

  Widget _buildChartResultView() {
    if (_horaryViewModel.chartData == null) {
      return const Center(child: Text('無卜卦數據'));
    }

    // 導航到新的卜卦結果頁面
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider.value(
            value: _horaryViewModel,
            child: HoraryResultPage(
              question: _questionController.text,
              chartData: _horaryViewModel.chartData!,
            ),
          ),
        ),
      ).then((_) {
        // 當從結果頁面返回時，重置狀態
        setState(() {
          _showChartResult = false;
        });
      });
    });

    // 顯示加載指示器，直到導航完成
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('正在準備卜卦結果...'),
        ],
      ),
    );
  }

  Widget _buildDivinationCard(String title, String subtitle, IconData icon, {required String content, Color? color}) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: color ?? AppColors.solarAmber,
                  radius: 24,
                  child: Icon(icon, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(fontSize: 14, color: Colors.black87),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            const Divider(),
            const SizedBox(height: 4),
            Text(
              content,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivinationTips() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: AppColors.solarAmber),
                SizedBox(width: 8),
                Text(
                  '卜卦小貼士',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Text(
              '1. 卜卦前保持平靜的心態，專注於您的問題。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '2. 問題應該明確具體，避免過於籠統或包含多個問題。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '3. 同一個問題不宜在短時間內重複卜問，以免干擾結果。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '4. 卜卦結果提供的是參考和啟示，最終決定仍應基於理性思考。',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '5. 變爻代表事物的變化趨勢，無變爻則表示情況相對穩定。',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
