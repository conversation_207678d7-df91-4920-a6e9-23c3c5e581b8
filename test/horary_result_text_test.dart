import 'package:astreal/models/aspect_info.dart';
import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/models/planet_position.dart';
import 'package:astreal/viewmodels/horary_chart_viewmodel.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('卜卦結果文本測試', () {
    late HoraryChartViewModel viewModel;

    setUp(() {
      viewModel = HoraryChartViewModel();
    });

    test('無卜卦數據時應返回提示信息', () {
      final result = viewModel.getHoraryResultText();
      expect(result, '尚未進行卜卦');
    });

    test('有卜卦數據時應生成詳細結果', () {
      // 創建測試用的卜卦數據
      final testChartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: BirthData(
          id: 'test_horary',
          name: '測試卜卦',
          birthDate: DateTime(2024, 1, 15, 14, 30),
          birthPlace: '台北市',
          latitude: 25.0330,
          longitude: 121.5654,
          notes: '今年會找到理想的工作嗎？',
        ),
        planets: [
          PlanetPosition(
            id: 0,
            name: '太陽',
            symbol: '☉',
            longitude: 295.5, // 摩羯座 25°30'
            latitude: 0.0,
            distance: 1.0,
            longitudeSpeed: 1.0,
            latitudeSpeed: 0.0,
            distanceSpeed: 0.0,
            sign: '摩羯座',
            house: 10,
          ),
          PlanetPosition(
            id: 1,
            name: '月亮',
            symbol: '☽',
            longitude: 45.2, // 金牛座 15°12'
            latitude: 0.0,
            distance: 1.0,
            longitudeSpeed: 13.0,
            latitudeSpeed: 0.0,
            distanceSpeed: 0.0,
            sign: '金牛座',
            house: 2,
          ),
          PlanetPosition(
            id: 10,
            name: '上升',
            symbol: 'ASC',
            longitude: 120.0, // 獅子座 0°
            latitude: 0.0,
            distance: 0.0,
            longitudeSpeed: 0.0,
            latitudeSpeed: 0.0,
            distanceSpeed: 0.0,
            sign: '獅子座',
            house: 1,
          ),
          PlanetPosition(
            id: 11,
            name: '中天',
            symbol: 'MC',
            longitude: 30.0, // 金牛座 0°
            latitude: 0.0,
            distance: 0.0,
            longitudeSpeed: 0.0,
            latitudeSpeed: 0.0,
            distanceSpeed: 0.0,
            sign: '金牛座',
            house: 10,
          ),
        ],
        aspects: [
          AspectInfo(
            planet1: PlanetPosition(
              id: 0,
              name: '太陽',
              symbol: '☉',
              longitude: 295.5,
              latitude: 0.0,
              distance: 1.0,
              longitudeSpeed: 1.0,
              latitudeSpeed: 0.0,
              distanceSpeed: 0.0,
              sign: '摩羯座',
              house: 10,
            ),
            planet2: PlanetPosition(
              id: 1,
              name: '月亮',
              symbol: '☽',
              longitude: 45.2,
              latitude: 0.0,
              distance: 1.0,
              longitudeSpeed: 13.0,
              latitudeSpeed: 0.0,
              distanceSpeed: 0.0,
              sign: '金牛座',
              house: 2,
            ),
            aspect: '三分相',
            shortZh: '拱',
            symbol: '△',
            angle: 120,
            orb: 5.3,
            direction: AspectDirection.applying,
          ),
        ],
      );

      // 設置測試數據到 viewModel
      viewModel.setTestChartData(testChartData);

      final result = viewModel.getHoraryResultText();

      // 驗證結果包含預期的內容
      expect(result, contains('占星卜卦結果'));
      expect(result, contains('今年會找到理想的工作嗎？'));
      expect(result, contains('2024-01-15 14:30'));
      expect(result, contains('台北市'));
      expect(result, contains('上升點'));
      expect(result, contains('中天'));
      expect(result, contains('太陽'));
      expect(result, contains('月亮'));
      expect(result, contains('摩羯座'));
      expect(result, contains('金牛座'));
      expect(result, contains('三分相'));
      expect(result, contains('入相'));
      expect(result, contains('卜卦分析要點'));
      expect(result, contains('判斷提示'));
    });

    test('應該正確格式化度數分鐘', () {
      // 這個測試驗證度數格式化功能
      final testChartData = ChartData(
        chartType: ChartType.horary,
        primaryPerson: BirthData(
          id: 'test_format',
          name: '格式測試',
          birthDate: DateTime(2024, 1, 15, 14, 30),
          birthPlace: '台北市',
          latitude: 25.0330,
          longitude: 121.5654,
          notes: '測試度數格式',
        ),
        planets: [
          PlanetPosition(
            id: 0,
            name: '太陽',
            symbol: '☉',
            longitude: 15.75, // 15°45'
            latitude: 0.0,
            distance: 1.0,
            longitudeSpeed: 1.0,
            latitudeSpeed: 0.0,
            distanceSpeed: 0.0,
            sign: '白羊座',
            house: 1,
          ),
        ],
      );

      viewModel.setTestChartData(testChartData);
      final result = viewModel.getHoraryResultText();

      // 驗證度數格式（15°45'）
      expect(result, contains('15°45\''));
    });
  });
}
